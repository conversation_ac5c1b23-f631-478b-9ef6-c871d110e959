window.jest_html_reporters_callback__({"numFailedTestSuites":2,"numFailedTests":7,"numPassedTestSuites":12,"numPassedTests":45,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":14,"numTotalTests":52,"startTime":1750689272526,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":2,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750689275760,"runtime":2917,"slow":false,"start":1750689272843},"testFilePath":"/home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Refund System Integration Tests","refundStatus Default Value Tests"],"duration":222,"failureMessages":[],"fullName":"Refund System Integration Tests refundStatus Default Value Tests should verify refundStatus default value is NONE","status":"passed","title":"should verify refundStatus default value is NONE"},{"ancestorTitles":["Refund System Integration Tests","refundStatus Default Value Tests"],"duration":119,"failureMessages":[],"fullName":"Refund System Integration Tests refundStatus Default Value Tests should verify refund status enum values","status":"passed","title":"should verify refund status enum values"}]},{"numFailingTests":0,"numPassingTests":9,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750689279027,"runtime":6195,"slow":true,"start":1750689272832},"testFilePath":"/home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Real Stripe Payment Integration Tests","Real Payment Intent Flow"],"duration":320,"failureMessages":[],"fullName":"Real Stripe Payment Integration Tests Real Payment Intent Flow should create real payment intent with Stripe","status":"passed","title":"should create real payment intent with Stripe"},{"ancestorTitles":["Real Stripe Payment Integration Tests","Real Payment Intent Flow"],"duration":47,"failureMessages":[],"fullName":"Real Stripe Payment Integration Tests Real Payment Intent Flow should retrieve real payment intent status","status":"passed","title":"should retrieve real payment intent status"},{"ancestorTitles":["Real Stripe Payment Integration Tests","Real Payment Intent Flow"],"duration":57,"failureMessages":[],"fullName":"Real Stripe Payment Integration Tests Real Payment Intent Flow should handle payment with test card","status":"passed","title":"should handle payment with test card"},{"ancestorTitles":["Real Stripe Payment Integration Tests","Real Payment Intent Flow"],"duration":95,"failureMessages":[],"fullName":"Real Stripe Payment Integration Tests Real Payment Intent Flow should handle declined card","status":"passed","title":"should handle declined card"},{"ancestorTitles":["Real Stripe Payment Integration Tests","Real Customer Management"],"duration":79,"failureMessages":[],"fullName":"Real Stripe Payment Integration Tests Real Customer Management should create real Stripe customer","status":"passed","title":"should create real Stripe customer"},{"ancestorTitles":["Real Stripe Payment Integration Tests","Real Customer Management"],"duration":48,"failureMessages":[],"fullName":"Real Stripe Payment Integration Tests Real Customer Management should retrieve real Stripe customer","status":"passed","title":"should retrieve real Stripe customer"},{"ancestorTitles":["Real Stripe Payment Integration Tests","Real Webhook Processing"],"duration":46,"failureMessages":[],"fullName":"Real Stripe Payment Integration Tests Real Webhook Processing should process real webhook signature validation","status":"passed","title":"should process real webhook signature validation"},{"ancestorTitles":["Real Stripe Payment Integration Tests","Error Handling with Real API"],"duration":51,"failureMessages":[],"fullName":"Real Stripe Payment Integration Tests Error Handling with Real API should handle invalid amount","status":"passed","title":"should handle invalid amount"},{"ancestorTitles":["Real Stripe Payment Integration Tests","Error Handling with Real API"],"duration":45,"failureMessages":[],"fullName":"Real Stripe Payment Integration Tests Error Handling with Real API should handle invalid currency","status":"passed","title":"should handle invalid currency"}]},{"numFailingTests":0,"numPassingTests":1,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750689279415,"runtime":6589,"slow":true,"start":1750689272826},"testFilePath":"/home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paymentSystem.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Payment System Integration Tests (GraphQL Schema)","Payment Related GraphQL Schema"],"duration":359,"failureMessages":[],"fullName":"Payment System Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have payment related types in schema","status":"passed","title":"should have payment related types in schema"}]},{"numFailingTests":0,"numPassingTests":2,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750689279569,"runtime":6747,"slow":true,"start":1750689272822},"testFilePath":"/home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Order Management Integration Tests","Order GraphQL Schema Tests"],"duration":306,"failureMessages":[],"fullName":"Order Management Integration Tests Order GraphQL Schema Tests should have order query in schema","status":"passed","title":"should have order query in schema"},{"ancestorTitles":["Order Management Integration Tests","Order GraphQL Schema Tests"],"duration":81,"failureMessages":[],"fullName":"Order Management Integration Tests Order GraphQL Schema Tests should have orders query in schema","status":"passed","title":"should have orders query in schema"}]},{"numFailingTests":0,"numPassingTests":2,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750689279575,"runtime":6770,"slow":true,"start":1750689272805},"testFilePath":"/home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["PayPal Payment Integration Tests (GraphQL Schema)","Payment Related GraphQL Schema"],"duration":317,"failureMessages":[],"fullName":"PayPal Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have payment related types in schema","status":"passed","title":"should have payment related types in schema"},{"ancestorTitles":["PayPal Payment Integration Tests (GraphQL Schema)","Payment Related GraphQL Schema"],"duration":80,"failureMessages":[],"fullName":"PayPal Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have query types in schema","status":"passed","title":"should have query types in schema"}]},{"numFailingTests":0,"numPassingTests":2,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750689279588,"runtime":6746,"slow":true,"start":1750689272842},"testFilePath":"/home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderNotifications.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Order Notifications Integration Tests (GraphQL Schema)","GraphQL Subscription Schema Tests"],"duration":365,"failureMessages":[],"fullName":"Order Notifications Integration Tests (GraphQL Schema) GraphQL Subscription Schema Tests should have order subscription in schema","status":"passed","title":"should have order subscription in schema"},{"ancestorTitles":["Order Notifications Integration Tests (GraphQL Schema)","GraphQL Subscription Schema Tests"],"duration":90,"failureMessages":[],"fullName":"Order Notifications Integration Tests (GraphQL Schema) GraphQL Subscription Schema Tests should have mutation types in schema","status":"passed","title":"should have mutation types in schema"}]},{"numFailingTests":0,"numPassingTests":3,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750689279598,"runtime":6780,"slow":true,"start":1750689272818},"testFilePath":"/home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Order State Machine Integration Tests (GraphQL)","GraphQL Order Status Updates"],"duration":343,"failureMessages":[],"fullName":"Order State Machine Integration Tests (GraphQL) GraphQL Order Status Updates should have updateOrderStatus mutation in schema","status":"passed","title":"should have updateOrderStatus mutation in schema"},{"ancestorTitles":["Order State Machine Integration Tests (GraphQL)","GraphQL Order Status Updates"],"duration":82,"failureMessages":[],"fullName":"Order State Machine Integration Tests (GraphQL) GraphQL Order Status Updates should have Order type with orderStatus field in schema","status":"passed","title":"should have Order type with orderStatus field in schema"},{"ancestorTitles":["Order State Machine Integration Tests (GraphQL)","GraphQL Order Status Updates"],"duration":73,"failureMessages":[],"fullName":"Order State Machine Integration Tests (GraphQL) GraphQL Order Status Updates should have order status enum values in schema","status":"passed","title":"should have order status enum values in schema"}]},{"numFailingTests":0,"numPassingTests":2,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750689279601,"runtime":6803,"slow":true,"start":1750689272798},"testFilePath":"/home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Stripe Payment Integration Tests (GraphQL Schema)","Payment Related GraphQL Schema"],"duration":314,"failureMessages":[],"fullName":"Stripe Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have payment related types in schema","status":"passed","title":"should have payment related types in schema"},{"ancestorTitles":["Stripe Payment Integration Tests (GraphQL Schema)","Payment Related GraphQL Schema"],"duration":82,"failureMessages":[],"fullName":"Stripe Payment Integration Tests (GraphQL Schema) Payment Related GraphQL Schema should have mutation types in schema","status":"passed","title":"should have mutation types in schema"}]},{"numFailingTests":0,"numPassingTests":5,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750689279781,"runtime":6956,"slow":true,"start":1750689272825},"testFilePath":"/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Customer GraphQL API Integration Tests","Customer Schema Types"],"duration":362,"failureMessages":[],"fullName":"Customer GraphQL API Integration Tests Customer Schema Types should have Customer type in schema","status":"passed","title":"should have Customer type in schema"},{"ancestorTitles":["Customer GraphQL API Integration Tests","Customer Schema Types"],"duration":88,"failureMessages":[],"fullName":"Customer GraphQL API Integration Tests Customer Schema Types should have Address type in schema","status":"passed","title":"should have Address type in schema"},{"ancestorTitles":["Customer GraphQL API Integration Tests","Customer Input Types"],"duration":76,"failureMessages":[],"fullName":"Customer GraphQL API Integration Tests Customer Input Types should have AddressInput type in schema","status":"passed","title":"should have AddressInput type in schema"},{"ancestorTitles":["Customer GraphQL API Integration Tests","Customer Operations"],"duration":78,"failureMessages":[],"fullName":"Customer GraphQL API Integration Tests Customer Operations should validate customer-related mutations exist","status":"passed","title":"should validate customer-related mutations exist"},{"ancestorTitles":["Customer GraphQL API Integration Tests","Customer Operations"],"duration":76,"failureMessages":[],"fullName":"Customer GraphQL API Integration Tests Customer Operations should handle GraphQL validation errors","status":"passed","title":"should handle GraphQL validation errors"}]},{"numFailingTests":0,"numPassingTests":9,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750689280020,"runtime":7219,"slow":true,"start":1750689272801},"testFilePath":"/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Restaurant GraphQL API Integration Tests","GraphQL Endpoint"],"duration":348,"failureMessages":[],"fullName":"Restaurant GraphQL API Integration Tests GraphQL Endpoint should respond to GraphQL endpoint","status":"passed","title":"should respond to GraphQL endpoint"},{"ancestorTitles":["Restaurant GraphQL API Integration Tests","GraphQL Endpoint"],"duration":102,"failureMessages":[],"fullName":"Restaurant GraphQL API Integration Tests GraphQL Endpoint should handle invalid GraphQL queries","status":"passed","title":"should handle invalid GraphQL queries"},{"ancestorTitles":["Restaurant GraphQL API Integration Tests","Restaurant Schema"],"duration":65,"failureMessages":[],"fullName":"Restaurant GraphQL API Integration Tests Restaurant Schema should have Restaurant type in schema","status":"passed","title":"should have Restaurant type in schema"},{"ancestorTitles":["Restaurant GraphQL API Integration Tests","Basic GraphQL Operations"],"duration":61,"failureMessages":[],"fullName":"Restaurant GraphQL API Integration Tests Basic GraphQL Operations should handle simple queries","status":"passed","title":"should handle simple queries"},{"ancestorTitles":["Restaurant GraphQL API Integration Tests","Basic GraphQL Operations"],"duration":70,"failureMessages":[],"fullName":"Restaurant GraphQL API Integration Tests Basic GraphQL Operations should validate GraphQL syntax","status":"passed","title":"should validate GraphQL syntax"},{"ancestorTitles":["Restaurant GraphQL API Integration Tests","Basic GraphQL Operations"],"duration":54,"failureMessages":[],"fullName":"Restaurant GraphQL API Integration Tests Basic GraphQL Operations should handle empty queries","status":"passed","title":"should handle empty queries"},{"ancestorTitles":["Restaurant GraphQL API Integration Tests","Schema Introspection"],"duration":49,"failureMessages":[],"fullName":"Restaurant GraphQL API Integration Tests Schema Introspection should support schema introspection","status":"passed","title":"should support schema introspection"},{"ancestorTitles":["Restaurant GraphQL API Integration Tests","Schema Introspection"],"duration":45,"failureMessages":[],"fullName":"Restaurant GraphQL API Integration Tests Schema Introspection should list available queries","status":"passed","title":"should list available queries"},{"ancestorTitles":["Restaurant GraphQL API Integration Tests","Schema Introspection"],"duration":62,"failureMessages":[],"fullName":"Restaurant GraphQL API Integration Tests Schema Introspection should list available mutations","status":"passed","title":"should list available mutations"}]},{"numFailingTests":3,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750689280094,"runtime":7296,"slow":true,"start":1750689272798},"testFilePath":"/home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js","failureMessage":"\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWhatsApp Webhook Integration › should process incoming message and create session\u001b[39m\u001b[22m\n\n    MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 14 |\u001b[39m     \u001b[36mtry\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 15 |\u001b[39m       \u001b[90m// Connect to MongoDB test container\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 16 |\u001b[39m       \u001b[36mawait\u001b[39m mongoose\u001b[33m.\u001b[39mconnect(global\u001b[33m.\u001b[39m__MONGO_URI__\u001b[33m,\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                      \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 17 |\u001b[39m         useNewUrlParser\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 18 |\u001b[39m         useUnifiedTopology\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 19 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat NativeConnection.Object.<anonymous>.Connection.openUri (\u001b[22m\u001b[2mnode_modules/mongoose/lib/connection.js\u001b[2m:695:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:414:10\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/mongoose/lib/helpers/promiseOrCallback.js\u001b[2m:41:5\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat promiseOrCallback (\u001b[22m\u001b[2mnode_modules/mongoose/lib/helpers/promiseOrCallback.js\u001b[2m:40:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (\u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:1290:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Mongoose.Object.<anonymous>.Mongoose.connect (\u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:413:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat connect (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:16:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat tryCatch (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:45:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:133:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:74:21)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:3:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:17:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:22:7\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:14:12\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat apply (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:33:2)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat apply (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:12:29)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat connectTestDB (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest/integration/whatsapp/webhook.test.js\u001b[39m\u001b[0m\u001b[2m:20:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat tryCatch (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:45:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:133:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:74:21)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:3:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:17:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:22:7\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:14:12)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWhatsApp Webhook Integration › should reject webhook with invalid signature\u001b[39m\u001b[22m\n\n    MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 14 |\u001b[39m     \u001b[36mtry\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 15 |\u001b[39m       \u001b[90m// Connect to MongoDB test container\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 16 |\u001b[39m       \u001b[36mawait\u001b[39m mongoose\u001b[33m.\u001b[39mconnect(global\u001b[33m.\u001b[39m__MONGO_URI__\u001b[33m,\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                      \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 17 |\u001b[39m         useNewUrlParser\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 18 |\u001b[39m         useUnifiedTopology\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 19 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat NativeConnection.Object.<anonymous>.Connection.openUri (\u001b[22m\u001b[2mnode_modules/mongoose/lib/connection.js\u001b[2m:695:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:414:10\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/mongoose/lib/helpers/promiseOrCallback.js\u001b[2m:41:5\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat promiseOrCallback (\u001b[22m\u001b[2mnode_modules/mongoose/lib/helpers/promiseOrCallback.js\u001b[2m:40:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (\u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:1290:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Mongoose.Object.<anonymous>.Mongoose.connect (\u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:413:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat connect (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:16:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat tryCatch (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:45:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:133:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:74:21)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:3:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:17:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:22:7\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:14:12\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat apply (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:33:2)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat apply (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:12:29)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat connectTestDB (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest/integration/whatsapp/webhook.test.js\u001b[39m\u001b[0m\u001b[2m:20:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat tryCatch (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:45:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:133:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:74:21)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:3:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:17:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:22:7\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:14:12)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mWhatsApp Webhook Integration › should handle malformed webhook payload\u001b[39m\u001b[22m\n\n    MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 14 |\u001b[39m     \u001b[36mtry\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 15 |\u001b[39m       \u001b[90m// Connect to MongoDB test container\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 16 |\u001b[39m       \u001b[36mawait\u001b[39m mongoose\u001b[33m.\u001b[39mconnect(global\u001b[33m.\u001b[39m__MONGO_URI__\u001b[33m,\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                      \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 17 |\u001b[39m         useNewUrlParser\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 18 |\u001b[39m         useUnifiedTopology\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 19 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat NativeConnection.Object.<anonymous>.Connection.openUri (\u001b[22m\u001b[2mnode_modules/mongoose/lib/connection.js\u001b[2m:695:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:414:10\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/mongoose/lib/helpers/promiseOrCallback.js\u001b[2m:41:5\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat promiseOrCallback (\u001b[22m\u001b[2mnode_modules/mongoose/lib/helpers/promiseOrCallback.js\u001b[2m:40:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (\u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:1290:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Mongoose.Object.<anonymous>.Mongoose.connect (\u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:413:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat connect (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:16:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat tryCatch (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:45:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:133:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:74:21)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:3:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:17:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:22:7\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:14:12\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat apply (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:33:2)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat apply (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:12:29)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat connectTestDB (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest/integration/whatsapp/webhook.test.js\u001b[39m\u001b[0m\u001b[2m:20:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat tryCatch (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:45:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:133:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:74:21)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:3:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:17:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:22:7\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:14:12)\u001b[22m\u001b[2m\u001b[22m\n","testResults":[{"ancestorTitles":["WhatsApp Webhook Integration"],"duration":7,"failureMessages":["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js:20:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"],"fullName":"WhatsApp Webhook Integration should process incoming message and create session","status":"failed","title":"should process incoming message and create session"},{"ancestorTitles":["WhatsApp Webhook Integration"],"duration":0,"failureMessages":["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js:20:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"],"fullName":"WhatsApp Webhook Integration should reject webhook with invalid signature","status":"failed","title":"should reject webhook with invalid signature"},{"ancestorTitles":["WhatsApp Webhook Integration"],"duration":0,"failureMessages":["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js:20:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"],"fullName":"WhatsApp Webhook Integration should handle malformed webhook payload","status":"failed","title":"should handle malformed webhook payload"}]},{"numFailingTests":4,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750689280117,"runtime":7312,"slow":true,"start":1750689272805},"testFilePath":"/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js","failureMessage":"\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mOrder GraphQL Mutations › should create a new order\u001b[39m\u001b[22m\n\n    MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 14 |\u001b[39m     \u001b[36mtry\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 15 |\u001b[39m       \u001b[90m// Connect to MongoDB test container\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 16 |\u001b[39m       \u001b[36mawait\u001b[39m mongoose\u001b[33m.\u001b[39mconnect(global\u001b[33m.\u001b[39m__MONGO_URI__\u001b[33m,\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                      \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 17 |\u001b[39m         useNewUrlParser\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 18 |\u001b[39m         useUnifiedTopology\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 19 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat NativeConnection.Object.<anonymous>.Connection.openUri (\u001b[22m\u001b[2mnode_modules/mongoose/lib/connection.js\u001b[2m:695:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:414:10\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/mongoose/lib/helpers/promiseOrCallback.js\u001b[2m:41:5\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat promiseOrCallback (\u001b[22m\u001b[2mnode_modules/mongoose/lib/helpers/promiseOrCallback.js\u001b[2m:40:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (\u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:1290:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Mongoose.Object.<anonymous>.Mongoose.connect (\u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:413:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat connect (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:16:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat tryCatch (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:45:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:133:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:74:21)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:3:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:17:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:22:7\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:14:12\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat apply (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:33:2)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat apply (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:12:29)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat connectTestDB (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest/integration/graphql/orderMutations.test.js\u001b[39m\u001b[0m\u001b[2m:17:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat tryCatch (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:45:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:133:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:74:21)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:3:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:17:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:22:7\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:14:12)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mOrder GraphQL Mutations › should fail to create order with invalid restaurant ID\u001b[39m\u001b[22m\n\n    MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 14 |\u001b[39m     \u001b[36mtry\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 15 |\u001b[39m       \u001b[90m// Connect to MongoDB test container\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 16 |\u001b[39m       \u001b[36mawait\u001b[39m mongoose\u001b[33m.\u001b[39mconnect(global\u001b[33m.\u001b[39m__MONGO_URI__\u001b[33m,\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                      \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 17 |\u001b[39m         useNewUrlParser\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 18 |\u001b[39m         useUnifiedTopology\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 19 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat NativeConnection.Object.<anonymous>.Connection.openUri (\u001b[22m\u001b[2mnode_modules/mongoose/lib/connection.js\u001b[2m:695:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:414:10\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/mongoose/lib/helpers/promiseOrCallback.js\u001b[2m:41:5\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat promiseOrCallback (\u001b[22m\u001b[2mnode_modules/mongoose/lib/helpers/promiseOrCallback.js\u001b[2m:40:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (\u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:1290:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Mongoose.Object.<anonymous>.Mongoose.connect (\u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:413:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat connect (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:16:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat tryCatch (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:45:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:133:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:74:21)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:3:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:17:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:22:7\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:14:12\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat apply (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:33:2)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat apply (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:12:29)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat connectTestDB (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest/integration/graphql/orderMutations.test.js\u001b[39m\u001b[0m\u001b[2m:17:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat tryCatch (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:45:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:133:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:74:21)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:3:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:17:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:22:7\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:14:12)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mOrder GraphQL Mutations › should fail to create order without authentication\u001b[39m\u001b[22m\n\n    MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 14 |\u001b[39m     \u001b[36mtry\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 15 |\u001b[39m       \u001b[90m// Connect to MongoDB test container\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 16 |\u001b[39m       \u001b[36mawait\u001b[39m mongoose\u001b[33m.\u001b[39mconnect(global\u001b[33m.\u001b[39m__MONGO_URI__\u001b[33m,\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                      \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 17 |\u001b[39m         useNewUrlParser\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 18 |\u001b[39m         useUnifiedTopology\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 19 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat NativeConnection.Object.<anonymous>.Connection.openUri (\u001b[22m\u001b[2mnode_modules/mongoose/lib/connection.js\u001b[2m:695:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:414:10\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/mongoose/lib/helpers/promiseOrCallback.js\u001b[2m:41:5\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat promiseOrCallback (\u001b[22m\u001b[2mnode_modules/mongoose/lib/helpers/promiseOrCallback.js\u001b[2m:40:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (\u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:1290:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Mongoose.Object.<anonymous>.Mongoose.connect (\u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:413:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat connect (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:16:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat tryCatch (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:45:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:133:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:74:21)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:3:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:17:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:22:7\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:14:12\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat apply (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:33:2)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat apply (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:12:29)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat connectTestDB (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest/integration/graphql/orderMutations.test.js\u001b[39m\u001b[0m\u001b[2m:17:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat tryCatch (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:45:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:133:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:74:21)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:3:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:17:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:22:7\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:14:12)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mOrder GraphQL Mutations › should update order status\u001b[39m\u001b[22m\n\n    MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 14 |\u001b[39m     \u001b[36mtry\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 15 |\u001b[39m       \u001b[90m// Connect to MongoDB test container\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 16 |\u001b[39m       \u001b[36mawait\u001b[39m mongoose\u001b[33m.\u001b[39mconnect(global\u001b[33m.\u001b[39m__MONGO_URI__\u001b[33m,\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                      \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 17 |\u001b[39m         useNewUrlParser\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[33m,\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 18 |\u001b[39m         useUnifiedTopology\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 19 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat NativeConnection.Object.<anonymous>.Connection.openUri (\u001b[22m\u001b[2mnode_modules/mongoose/lib/connection.js\u001b[2m:695:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:414:10\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/mongoose/lib/helpers/promiseOrCallback.js\u001b[2m:41:5\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat promiseOrCallback (\u001b[22m\u001b[2mnode_modules/mongoose/lib/helpers/promiseOrCallback.js\u001b[2m:40:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (\u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:1290:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Mongoose.Object.<anonymous>.Mongoose.connect (\u001b[22m\u001b[2mnode_modules/mongoose/lib/index.js\u001b[2m:413:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat connect (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:16:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat tryCatch (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:45:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:133:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:74:21)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:3:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:17:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:22:7\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:14:12\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat apply (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:33:2)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat apply (\u001b[22m\u001b[2mtest/helpers/dbHelper.js\u001b[2m:12:29)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat connectTestDB (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtest/integration/graphql/orderMutations.test.js\u001b[39m\u001b[0m\u001b[2m:17:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat tryCatch (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:45:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:133:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Generator.next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/regeneratorRuntime.js\u001b[2m:74:21)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat asyncGeneratorStep (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:3:17)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat _next (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:17:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:22:7\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2mnode_modules/@babel/runtime/helpers/asyncToGenerator.js\u001b[2m:14:12)\u001b[22m\u001b[2m\u001b[22m\n","testResults":[{"ancestorTitles":["Order GraphQL Mutations"],"duration":8,"failureMessages":["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"],"fullName":"Order GraphQL Mutations should create a new order","status":"failed","title":"should create a new order"},{"ancestorTitles":["Order GraphQL Mutations"],"duration":1,"failureMessages":["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"],"fullName":"Order GraphQL Mutations should fail to create order with invalid restaurant ID","status":"failed","title":"should fail to create order with invalid restaurant ID"},{"ancestorTitles":["Order GraphQL Mutations"],"duration":1,"failureMessages":["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"],"fullName":"Order GraphQL Mutations should fail to create order without authentication","status":"failed","title":"should fail to create order without authentication"},{"ancestorTitles":["Order GraphQL Mutations"],"duration":0,"failureMessages":["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"],"fullName":"Order GraphQL Mutations should update order status","status":"failed","title":"should update order status"}]},{"numFailingTests":0,"numPassingTests":2,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750689281935,"runtime":9120,"slow":true,"start":1750689272815},"testFilePath":"/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["GraphQL Queries Integration Tests","GraphQL Schema Introspection"],"duration":234,"failureMessages":[],"fullName":"GraphQL Queries Integration Tests GraphQL Schema Introspection should respond to GraphQL introspection query","status":"passed","title":"should respond to GraphQL introspection query"},{"ancestorTitles":["GraphQL Queries Integration Tests","GraphQL Schema Introspection"],"duration":54,"failureMessages":[],"fullName":"GraphQL Queries Integration Tests GraphQL Schema Introspection should have Restaurant type in schema","status":"passed","title":"should have Restaurant type in schema"}]},{"numFailingTests":0,"numPassingTests":6,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750689282124,"runtime":9327,"slow":true,"start":1750689272797},"testFilePath":"/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js","failureMessage":null,"testResults":[{"ancestorTitles":["Order GraphQL API Integration Tests","Order Schema Types"],"duration":224,"failureMessages":[],"fullName":"Order GraphQL API Integration Tests Order Schema Types should have Order type in schema","status":"passed","title":"should have Order type in schema"},{"ancestorTitles":["Order GraphQL API Integration Tests","Order Schema Types"],"duration":46,"failureMessages":[],"fullName":"Order GraphQL API Integration Tests Order Schema Types should have OrderStatus enum in schema","status":"passed","title":"should have OrderStatus enum in schema"},{"ancestorTitles":["Order GraphQL API Integration Tests","Order Input Types"],"duration":34,"failureMessages":[],"fullName":"Order GraphQL API Integration Tests Order Input Types should have OrderInput type in schema","status":"passed","title":"should have OrderInput type in schema"},{"ancestorTitles":["Order GraphQL API Integration Tests","Order Input Types"],"duration":52,"failureMessages":[],"fullName":"Order GraphQL API Integration Tests Order Input Types should validate GraphQL order operations","status":"passed","title":"should validate GraphQL order operations"},{"ancestorTitles":["Order GraphQL API Integration Tests","GraphQL Error Handling"],"duration":55,"failureMessages":[],"fullName":"Order GraphQL API Integration Tests GraphQL Error Handling should handle malformed order queries","status":"passed","title":"should handle malformed order queries"},{"ancestorTitles":["Order GraphQL API Integration Tests","GraphQL Error Handling"],"duration":42,"failureMessages":[],"fullName":"Order GraphQL API Integration Tests GraphQL Error Handling should validate required arguments","status":"passed","title":"should validate required arguments"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":[],"coverageDirectory":"/home/<USER>/firespoon/Firespoon_API_TF/test/coverage","coverageProvider":"babel","coverageReporters":["text","text-summary","lcov","clover","html","json"],"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":true,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":15,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[{"displayName":{"name":"UNIT","color":"blue"},"testEnvironment":"node","rootDir":"/home/<USER>/firespoon/Firespoon_API_TF","testMatch":["<rootDir>/test/unit/**/*.test.js"],"setupFilesAfterEnv":["<rootDir>/test/config/unit.setup.js","<rootDir>/test/config/enhanced-expect.js"],"transform":{"^.+\\.js$":"/home/<USER>/firespoon/Firespoon_API_TF/node_modules/babel-jest/build/index.js"},"moduleFileExtensions":["js","json","node"],"transformIgnorePatterns":["node_modules/"],"maxWorkers":"50%","resetModules":true,"clearMocks":true,"restoreMocks":true,"collectCoverageFrom":["graphql/**/*.js","models/**/*.js","routes/**/*.js","services/**/*.js","whatsapp/**/*.js","controllers/**/*.js","helpers/**/*.js","middleware/**/*.js","!**/node_modules/**","!**/test/**","!**/coverage/**"],"coverageThreshold":{"global":{"statements":75,"branches":65,"functions":75,"lines":75}},"coverageDirectory":"<rootDir>/test/coverage/unit"},{"displayName":{"name":"INTEGRATION","color":"green"},"testEnvironment":"node","rootDir":"/home/<USER>/firespoon/Firespoon_API_TF","testMatch":["<rootDir>/test/integration/**/*.test.js"],"setupFilesAfterEnv":["<rootDir>/test/config/integration.setup.js","<rootDir>/test/config/enhanced-expect.js"],"globalSetup":"<rootDir>/test/config/globalSetup.js","globalTeardown":"<rootDir>/test/config/globalTeardown.js","transform":{"^.+\\.js$":"/home/<USER>/firespoon/Firespoon_API_TF/node_modules/babel-jest/build/index.js"},"moduleFileExtensions":["js","json","node"],"transformIgnorePatterns":["node_modules/"],"maxWorkers":1,"resetModules":false},{"displayName":{"name":"E2E","color":"magenta"},"testEnvironment":"node","rootDir":"/home/<USER>/firespoon/Firespoon_API_TF","testMatch":["<rootDir>/test/e2e/**/*.test.js"],"setupFilesAfterEnv":["<rootDir>/test/config/e2e.setup.js","<rootDir>/test/config/enhanced-expect.js"],"globalSetup":"<rootDir>/test/config/globalSetup.js","globalTeardown":"<rootDir>/test/config/globalTeardown.js","transform":{"^.+\\.js$":"/home/<USER>/firespoon/Firespoon_API_TF/node_modules/babel-jest/build/index.js"},"moduleFileExtensions":["js","json","node"],"transformIgnorePatterns":["node_modules/"],"maxWorkers":1,"resetModules":false},{"displayName":{"name":"PERFORMANCE","color":"yellow"},"testEnvironment":"node","rootDir":"/home/<USER>/firespoon/Firespoon_API_TF","testMatch":["<rootDir>/test/performance/**/*.test.js"],"setupFilesAfterEnv":["<rootDir>/test/config/performance.setup.js","<rootDir>/test/config/enhanced-expect.js"],"globalSetup":"<rootDir>/test/config/globalSetup.js","globalTeardown":"<rootDir>/test/config/globalTeardown.js","transform":{"^.+\\.js$":"/home/<USER>/firespoon/Firespoon_API_TF/node_modules/babel-jest/build/index.js"},"moduleFileExtensions":["js","json","node"],"transformIgnorePatterns":["node_modules/"],"maxWorkers":1,"resetModules":false}],"reporters":[["default",{}],["/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-junit/index.js",{"outputDirectory":"<rootDir>/test/reports","outputName":"test-results.xml","suiteName":"Firespoon API Tests","classNameTemplate":"{displayName}.{classname}","titleTemplate":"{title}","ancestorSeparator":" › ","usePathForSuiteName":true}],["/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-html-reporters/index.js",{"publicPath":"<rootDir>/test/reports","filename":"test-report.html","pageTitle":"Firespoon API 测试报告","hideIcon":false,"expand":false,"testCommand":"npm test","openReport":false,"failureMessageOnly":0,"enableMergeData":true,"dataMergeLevel":2,"inlineSource":false}],["/home/<USER>/firespoon/Firespoon_API_TF/test/config/customReporter.js",{"outputFile":"<rootDir>/test/reports/custom-report.json"}],["/home/<USER>/firespoon/Firespoon_API_TF/test/config/detailedLogReporter.js",{}]],"rootDir":"/home/<USER>/firespoon/Firespoon_API_TF","runTestsByPath":false,"seed":-2055040919,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPattern":"","testResultsProcessor":"/home/<USER>/firespoon/Firespoon_API_TF/test/config/testResultsProcessor.js","testSequencer":"/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@jest/test-sequencer/build/index.js","testTimeout":30000,"updateSnapshot":"new","useStderr":false,"verbose":false,"watch":false,"watchAll":false,"watchPlugins":[{"config":{},"path":"/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-watch-typeahead/build/file_name_plugin/plugin.js"},{"config":{},"path":"/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-watch-typeahead/build/test_name_plugin/plugin.js"}],"watchman":true,"workerThreads":false},"endTime":1750689282916,"_reporterOptions":{"publicPath":"/home/<USER>/firespoon/Firespoon_API_TF/test/reports","filename":"test-report.html","expand":false,"pageTitle":"Firespoon API 测试报告","hideIcon":false,"testCommand":"npm test","openReport":false,"failureMessageOnly":0,"enableMergeData":true,"dataMergeLevel":2,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})