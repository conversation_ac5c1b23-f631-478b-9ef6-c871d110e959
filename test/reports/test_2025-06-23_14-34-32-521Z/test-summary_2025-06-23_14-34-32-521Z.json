{"summary": {"totalTests": 52, "passedTests": 45, "failedTests": 7, "skippedTests": 0, "totalDuration": 10401, "success": false, "timestamp": "2025-06-23T14:34:42.927Z", "environment": "test"}, "projects": {"INTEGRATION": {"tests": [{"caseId": "I-AUTO-001", "title": "should verify refundStatus default value is NONE", "status": "passed", "duration": 222, "failureMessages": [], "ancestorTitles": ["Refund System Integration Tests", "refundStatus Default Value Tests"], "capturedLogs": 18}, {"caseId": "I-AUTO-002", "title": "should verify refund status enum values", "status": "passed", "duration": 119, "failureMessages": [], "ancestorTitles": ["Refund System Integration Tests", "refundStatus Default Value Tests"], "capturedLogs": 26}, {"caseId": "I-AUTO-001", "title": "should create real payment intent with <PERSON>e", "status": "passed", "duration": 320, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"], "capturedLogs": 45}, {"caseId": "I-AUTO-002", "title": "should retrieve real payment intent status", "status": "passed", "duration": 47, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"], "capturedLogs": 53}, {"caseId": "I-AUTO-003", "title": "should handle payment with test card", "status": "passed", "duration": 57, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"], "capturedLogs": 61}, {"caseId": "I-AUTO-004", "title": "should handle declined card", "status": "passed", "duration": 95, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Payment Intent Flow"], "capturedLogs": 69}, {"caseId": "I-AUTO-005", "title": "should create real Stripe customer", "status": "passed", "duration": 79, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Customer Management"], "capturedLogs": 77}, {"caseId": "I-AUTO-006", "title": "should retrieve real Stripe customer", "status": "passed", "duration": 48, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Customer Management"], "capturedLogs": 85}, {"caseId": "I-AUTO-007", "title": "should process real webhook signature validation", "status": "passed", "duration": 46, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Real Webhook Processing"], "capturedLogs": 93}, {"caseId": "I-AUTO-008", "title": "should handle invalid amount", "status": "passed", "duration": 51, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Error Handling with Real API"], "capturedLogs": 101}, {"caseId": "I-AUTO-009", "title": "should handle invalid currency", "status": "passed", "duration": 45, "failureMessages": [], "ancestorTitles": ["Real Stripe Payment Integration Tests", "Error Handling with Real API"], "capturedLogs": 109}, {"caseId": "I-AUTO-001", "title": "should have payment related types in schema", "status": "passed", "duration": 359, "failureMessages": [], "ancestorTitles": ["Payment System Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"], "capturedLogs": 154}, {"caseId": "I-AUTO-001", "title": "should have order query in schema", "status": "passed", "duration": 306, "failureMessages": [], "ancestorTitles": ["Order Management Integration Tests", "Order GraphQL Schema Tests"], "capturedLogs": 204}, {"caseId": "I-AUTO-002", "title": "should have orders query in schema", "status": "passed", "duration": 81, "failureMessages": [], "ancestorTitles": ["Order Management Integration Tests", "Order GraphQL Schema Tests"], "capturedLogs": 212}, {"caseId": "I-AUTO-001", "title": "should have payment related types in schema", "status": "passed", "duration": 317, "failureMessages": [], "ancestorTitles": ["PayPal Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"], "capturedLogs": 225}, {"caseId": "I-AUTO-002", "title": "should have query types in schema", "status": "passed", "duration": 80, "failureMessages": [], "ancestorTitles": ["PayPal Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"], "capturedLogs": 233}, {"caseId": "I-AUTO-001", "title": "should have order subscription in schema", "status": "passed", "duration": 365, "failureMessages": [], "ancestorTitles": ["Order Notifications Integration Tests (GraphQL Schema)", "GraphQL Subscription Schema Tests"], "capturedLogs": 245}, {"caseId": "I-AUTO-002", "title": "should have mutation types in schema", "status": "passed", "duration": 90, "failureMessages": [], "ancestorTitles": ["Order Notifications Integration Tests (GraphQL Schema)", "GraphQL Subscription Schema Tests"], "capturedLogs": 253}, {"caseId": "I-AUTO-001", "title": "should have updateOrderStatus mutation in schema", "status": "passed", "duration": 343, "failureMessages": [], "ancestorTitles": ["Order State Machine Integration Tests (GraphQL)", "GraphQL Order Status Updates"], "capturedLogs": 263}, {"caseId": "I-AUTO-002", "title": "should have Order type with orderStatus field in schema", "status": "passed", "duration": 82, "failureMessages": [], "ancestorTitles": ["Order State Machine Integration Tests (GraphQL)", "GraphQL Order Status Updates"], "capturedLogs": 271}, {"caseId": "I-AUTO-003", "title": "should have order status enum values in schema", "status": "passed", "duration": 73, "failureMessages": [], "ancestorTitles": ["Order State Machine Integration Tests (GraphQL)", "GraphQL Order Status Updates"], "capturedLogs": 279}, {"caseId": "I-AUTO-001", "title": "should have payment related types in schema", "status": "passed", "duration": 314, "failureMessages": [], "ancestorTitles": ["Stripe Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"], "capturedLogs": 289}, {"caseId": "I-AUTO-002", "title": "should have mutation types in schema", "status": "passed", "duration": 82, "failureMessages": [], "ancestorTitles": ["Stripe Payment Integration Tests (GraphQL Schema)", "Payment Related GraphQL Schema"], "capturedLogs": 297}, {"caseId": "I-AUTO-001", "title": "should have Customer type in schema", "status": "passed", "duration": 362, "failureMessages": [], "ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Schema Types"], "capturedLogs": 315}, {"caseId": "I-AUTO-002", "title": "should have Address type in schema", "status": "passed", "duration": 88, "failureMessages": [], "ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Schema Types"], "capturedLogs": 323}, {"caseId": "I-AUTO-003", "title": "should have AddressInput type in schema", "status": "passed", "duration": 76, "failureMessages": [], "ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Input Types"], "capturedLogs": 331}, {"caseId": "I-AUTO-004", "title": "should validate customer-related mutations exist", "status": "passed", "duration": 78, "failureMessages": [], "ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Operations"], "capturedLogs": 339}, {"caseId": "I-AUTO-005", "title": "should handle GraphQL validation errors", "status": "passed", "duration": 76, "failureMessages": [], "ancestorTitles": ["Customer GraphQL API Integration Tests", "Customer Operations"], "capturedLogs": 347}, {"caseId": "I-AUTO-001", "title": "should respond to GraphQL endpoint", "status": "passed", "duration": 348, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "GraphQL Endpoint"], "capturedLogs": 360}, {"caseId": "I-AUTO-002", "title": "should handle invalid GraphQL queries", "status": "passed", "duration": 102, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "GraphQL Endpoint"], "capturedLogs": 368}, {"caseId": "I-AUTO-003", "title": "should have Restaurant type in schema", "status": "passed", "duration": 65, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Restaurant Schema"], "capturedLogs": 376}, {"caseId": "I-AUTO-004", "title": "should handle simple queries", "status": "passed", "duration": 61, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Basic GraphQL Operations"], "capturedLogs": 384}, {"caseId": "I-AUTO-005", "title": "should validate GraphQL syntax", "status": "passed", "duration": 70, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Basic GraphQL Operations"], "capturedLogs": 392}, {"caseId": "I-AUTO-006", "title": "should handle empty queries", "status": "passed", "duration": 54, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Basic GraphQL Operations"], "capturedLogs": 400}, {"caseId": "I-AUTO-007", "title": "should support schema introspection", "status": "passed", "duration": 49, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Schema Introspection"], "capturedLogs": 408}, {"caseId": "I-AUTO-008", "title": "should list available queries", "status": "passed", "duration": 45, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Schema Introspection"], "capturedLogs": 416}, {"caseId": "I-AUTO-009", "title": "should list available mutations", "status": "passed", "duration": 62, "failureMessages": [], "ancestorTitles": ["Restaurant GraphQL API Integration Tests", "Schema Introspection"], "capturedLogs": 424}, {"caseId": "I-AUTO-001", "title": "should process incoming message and create session", "status": "failed", "duration": 7, "failureMessages": ["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js:20:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"], "ancestorTitles": ["WhatsApp Webhook Integration"], "capturedLogs": 437}, {"caseId": "I-AUTO-002", "title": "should reject webhook with invalid signature", "status": "failed", "duration": 0, "failureMessages": ["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js:20:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"], "ancestorTitles": ["WhatsApp Webhook Integration"], "capturedLogs": 445}, {"caseId": "I-AUTO-003", "title": "should handle malformed webhook payload", "status": "failed", "duration": 0, "failureMessages": ["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js:20:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"], "ancestorTitles": ["WhatsApp Webhook Integration"], "capturedLogs": 453}, {"caseId": "I-AUTO-001", "title": "should create a new order", "status": "failed", "duration": 8, "failureMessages": ["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"], "ancestorTitles": ["Order GraphQL Mutations"], "capturedLogs": 466}, {"caseId": "I-AUTO-002", "title": "should fail to create order with invalid restaurant ID", "status": "failed", "duration": 1, "failureMessages": ["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"], "ancestorTitles": ["Order GraphQL Mutations"], "capturedLogs": 474}, {"caseId": "I-AUTO-003", "title": "should fail to create order without authentication", "status": "failed", "duration": 1, "failureMessages": ["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"], "ancestorTitles": ["Order GraphQL Mutations"], "capturedLogs": 482}, {"caseId": "I-AUTO-004", "title": "should update order status", "status": "failed", "duration": 0, "failureMessages": ["MongooseError: The `uri` parameter to `openUri()` must be a string, got \"undefined\". Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.\n    at NativeConnection.Object.<anonymous>.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5\n    at new Promise (<anonymous>)\n    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)\n    at Mongoose.Object.<anonymous>.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)\n    at Mongoose.Object.<anonymous>.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)\n    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)\n    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)\n    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)\n    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)\n    at Generator.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)\n    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)\n    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)\n    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)\n    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7\n    at new Promise (<anonymous>)\n    at Object.<anonymous> (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)\n    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)\n    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)\n    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)"], "ancestorTitles": ["Order GraphQL Mutations"], "capturedLogs": 490}, {"caseId": "I-AUTO-001", "title": "should respond to GraphQL introspection query", "status": "passed", "duration": 234, "failureMessages": [], "ancestorTitles": ["GraphQL Queries Integration Tests", "GraphQL Schema Introspection"], "capturedLogs": 552}, {"caseId": "I-AUTO-002", "title": "should have Restaurant type in schema", "status": "passed", "duration": 54, "failureMessages": [], "ancestorTitles": ["GraphQL Queries Integration Tests", "GraphQL Schema Introspection"], "capturedLogs": 560}, {"caseId": "I-AUTO-001", "title": "should have Order type in schema", "status": "passed", "duration": 224, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "Order Schema Types"], "capturedLogs": 607}, {"caseId": "I-AUTO-002", "title": "should have OrderStatus enum in schema", "status": "passed", "duration": 46, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "Order Schema Types"], "capturedLogs": 615}, {"caseId": "I-AUTO-003", "title": "should have OrderInput type in schema", "status": "passed", "duration": 34, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "Order Input Types"], "capturedLogs": 623}, {"caseId": "I-AUTO-004", "title": "should validate GraphQL order operations", "status": "passed", "duration": 52, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "Order Input Types"], "capturedLogs": 631}, {"caseId": "I-AUTO-005", "title": "should handle malformed order queries", "status": "passed", "duration": 55, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "GraphQL Error Handling"], "capturedLogs": 639}, {"caseId": "I-AUTO-006", "title": "should validate required arguments", "status": "passed", "duration": 42, "failureMessages": [], "ancestorTitles": ["Order GraphQL API Integration Tests", "GraphQL Error Handling"], "capturedLogs": 647}], "summary": {"total": 52, "passed": 45, "failed": 7, "skipped": 0, "duration": 5915}}}, "coverage": {}, "performance": {}}