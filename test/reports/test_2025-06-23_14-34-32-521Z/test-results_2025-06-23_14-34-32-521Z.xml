<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Firespoon API Tests" tests="52" failures="7" errors="0" time="10.385">
  <testsuite name="Refund System Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-23T14:34:32" time="2.917" tests="2">
    <testcase classname="INTEGRATION.Refund System Integration Tests › refundStatus Default Value Tests" name="should verify refundStatus default value is NONE" time="0.222">
    </testcase>
    <testcase classname="INTEGRATION.Refund System Integration Tests › refundStatus Default Value Tests" name="should verify refund status enum values" time="0.119">
    </testcase>
  </testsuite>
  <testsuite name="Real Stripe Payment Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-23T14:34:32" time="6.195" tests="9">
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Real Payment Intent Flow" name="should create real payment intent with Stripe" time="0.32">
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Real Payment Intent Flow" name="should retrieve real payment intent status" time="0.047">
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Real Payment Intent Flow" name="should handle payment with test card" time="0.057">
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Real Payment Intent Flow" name="should handle declined card" time="0.095">
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Real Customer Management" name="should create real Stripe customer" time="0.079">
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Real Customer Management" name="should retrieve real Stripe customer" time="0.048">
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Real Webhook Processing" name="should process real webhook signature validation" time="0.046">
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Error Handling with Real API" name="should handle invalid amount" time="0.051">
    </testcase>
    <testcase classname="INTEGRATION.Real Stripe Payment Integration Tests › Error Handling with Real API" name="should handle invalid currency" time="0.045">
    </testcase>
  </testsuite>
  <testsuite name="Payment System Integration Tests (GraphQL Schema)" errors="0" failures="0" skipped="0" timestamp="2025-06-23T14:34:32" time="6.589" tests="1">
    <testcase classname="INTEGRATION.Payment System Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema" name="should have payment related types in schema" time="0.359">
    </testcase>
  </testsuite>
  <testsuite name="Order Management Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-23T14:34:32" time="6.747" tests="2">
    <testcase classname="INTEGRATION.Order Management Integration Tests › Order GraphQL Schema Tests" name="should have order query in schema" time="0.306">
    </testcase>
    <testcase classname="INTEGRATION.Order Management Integration Tests › Order GraphQL Schema Tests" name="should have orders query in schema" time="0.081">
    </testcase>
  </testsuite>
  <testsuite name="PayPal Payment Integration Tests (GraphQL Schema)" errors="0" failures="0" skipped="0" timestamp="2025-06-23T14:34:32" time="6.77" tests="2">
    <testcase classname="INTEGRATION.PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema" name="should have payment related types in schema" time="0.317">
    </testcase>
    <testcase classname="INTEGRATION.PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema" name="should have query types in schema" time="0.08">
    </testcase>
  </testsuite>
  <testsuite name="Order Notifications Integration Tests (GraphQL Schema)" errors="0" failures="0" skipped="0" timestamp="2025-06-23T14:34:32" time="6.746" tests="2">
    <testcase classname="INTEGRATION.Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests" name="should have order subscription in schema" time="0.365">
    </testcase>
    <testcase classname="INTEGRATION.Order Notifications Integration Tests (GraphQL Schema) › GraphQL Subscription Schema Tests" name="should have mutation types in schema" time="0.09">
    </testcase>
  </testsuite>
  <testsuite name="Order State Machine Integration Tests (GraphQL)" errors="0" failures="0" skipped="0" timestamp="2025-06-23T14:34:32" time="6.78" tests="3">
    <testcase classname="INTEGRATION.Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates" name="should have updateOrderStatus mutation in schema" time="0.343">
    </testcase>
    <testcase classname="INTEGRATION.Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates" name="should have Order type with orderStatus field in schema" time="0.082">
    </testcase>
    <testcase classname="INTEGRATION.Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates" name="should have order status enum values in schema" time="0.073">
    </testcase>
  </testsuite>
  <testsuite name="Stripe Payment Integration Tests (GraphQL Schema)" errors="0" failures="0" skipped="0" timestamp="2025-06-23T14:34:32" time="6.803" tests="2">
    <testcase classname="INTEGRATION.Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema" name="should have payment related types in schema" time="0.314">
    </testcase>
    <testcase classname="INTEGRATION.Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema" name="should have mutation types in schema" time="0.082">
    </testcase>
  </testsuite>
  <testsuite name="Customer GraphQL API Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-23T14:34:32" time="6.956" tests="5">
    <testcase classname="INTEGRATION.Customer GraphQL API Integration Tests › Customer Schema Types" name="should have Customer type in schema" time="0.362">
    </testcase>
    <testcase classname="INTEGRATION.Customer GraphQL API Integration Tests › Customer Schema Types" name="should have Address type in schema" time="0.088">
    </testcase>
    <testcase classname="INTEGRATION.Customer GraphQL API Integration Tests › Customer Input Types" name="should have AddressInput type in schema" time="0.076">
    </testcase>
    <testcase classname="INTEGRATION.Customer GraphQL API Integration Tests › Customer Operations" name="should validate customer-related mutations exist" time="0.078">
    </testcase>
    <testcase classname="INTEGRATION.Customer GraphQL API Integration Tests › Customer Operations" name="should handle GraphQL validation errors" time="0.076">
    </testcase>
  </testsuite>
  <testsuite name="Restaurant GraphQL API Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-23T14:34:32" time="7.219" tests="9">
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › GraphQL Endpoint" name="should respond to GraphQL endpoint" time="0.348">
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › GraphQL Endpoint" name="should handle invalid GraphQL queries" time="0.102">
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › Restaurant Schema" name="should have Restaurant type in schema" time="0.065">
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › Basic GraphQL Operations" name="should handle simple queries" time="0.061">
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › Basic GraphQL Operations" name="should validate GraphQL syntax" time="0.07">
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › Basic GraphQL Operations" name="should handle empty queries" time="0.054">
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › Schema Introspection" name="should support schema introspection" time="0.049">
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › Schema Introspection" name="should list available queries" time="0.045">
    </testcase>
    <testcase classname="INTEGRATION.Restaurant GraphQL API Integration Tests › Schema Introspection" name="should list available mutations" time="0.062">
    </testcase>
  </testsuite>
  <testsuite name="WhatsApp Webhook Integration" errors="0" failures="3" skipped="0" timestamp="2025-06-23T14:34:32" time="7.296" tests="3">
    <testcase classname="INTEGRATION.WhatsApp Webhook Integration" name="should process incoming message and create session" time="0.007">
      <failure>MongooseError: The `uri` parameter to `openUri()` must be a string, got &quot;undefined&quot;. Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
    at NativeConnection.Object.&lt;anonymous&gt;.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)
    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)
    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js:20:11)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="INTEGRATION.WhatsApp Webhook Integration" name="should reject webhook with invalid signature" time="0">
      <failure>MongooseError: The `uri` parameter to `openUri()` must be a string, got &quot;undefined&quot;. Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
    at NativeConnection.Object.&lt;anonymous&gt;.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)
    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)
    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js:20:11)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="INTEGRATION.WhatsApp Webhook Integration" name="should handle malformed webhook payload" time="0">
      <failure>MongooseError: The `uri` parameter to `openUri()` must be a string, got &quot;undefined&quot;. Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
    at NativeConnection.Object.&lt;anonymous&gt;.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)
    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)
    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js:20:11)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
  </testsuite>
  <testsuite name="Order GraphQL Mutations" errors="0" failures="4" skipped="0" timestamp="2025-06-23T14:34:32" time="7.312" tests="4">
    <testcase classname="INTEGRATION.Order GraphQL Mutations" name="should create a new order" time="0.008">
      <failure>MongooseError: The `uri` parameter to `openUri()` must be a string, got &quot;undefined&quot;. Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
    at NativeConnection.Object.&lt;anonymous&gt;.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)
    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)
    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL Mutations" name="should fail to create order with invalid restaurant ID" time="0.001">
      <failure>MongooseError: The `uri` parameter to `openUri()` must be a string, got &quot;undefined&quot;. Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
    at NativeConnection.Object.&lt;anonymous&gt;.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)
    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)
    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL Mutations" name="should fail to create order without authentication" time="0.001">
      <failure>MongooseError: The `uri` parameter to `openUri()` must be a string, got &quot;undefined&quot;. Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
    at NativeConnection.Object.&lt;anonymous&gt;.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)
    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)
    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL Mutations" name="should update order status" time="0">
      <failure>MongooseError: The `uri` parameter to `openUri()` must be a string, got &quot;undefined&quot;. Make sure the first parameter to `mongoose.connect()` or `mongoose.createConnection()` is a string.
    at NativeConnection.Object.&lt;anonymous&gt;.Connection.openUri (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/connection.js:695:11)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:414:10
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:41:5
    at new Promise (&lt;anonymous&gt;)
    at promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/helpers/promiseOrCallback.js:40:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose._promiseOrCallback (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:1290:10)
    at Mongoose.Object.&lt;anonymous&gt;.Mongoose.connect (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/mongoose/lib/index.js:413:20)
    at connect (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:16:22)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:33:2)
    at apply (/home/<USER>/firespoon/Firespoon_API_TF/test/helpers/dbHelper.js:12:29)
    at connectTestDB (/home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js:17:11)
    at tryCatch (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:45:16)
    at Generator.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:133:17)
    at Generator.next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/regeneratorRuntime.js:74:21)
    at asyncGeneratorStep (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:3:17)
    at _next (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:17:9)
    at /home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:22:7
    at new Promise (&lt;anonymous&gt;)
    at Object.&lt;anonymous&gt; (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/@babel/runtime/helpers/asyncToGenerator.js:14:12)
    at Promise.then.completed (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/utils.js:231:10)
    at _callCircusHook (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:281:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:95:7)
    at _runTestsForDescribeBlock (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:121:9)
    at run (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/run.js:71:3)
    at runAndTransformResultsToJestFormat (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)
    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)
    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)
    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)
    at Object.worker (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/testWorker.js:106:12)</failure>
    </testcase>
  </testsuite>
  <testsuite name="GraphQL Queries Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-23T14:34:32" time="9.12" tests="2">
    <testcase classname="INTEGRATION.GraphQL Queries Integration Tests › GraphQL Schema Introspection" name="should respond to GraphQL introspection query" time="0.234">
    </testcase>
    <testcase classname="INTEGRATION.GraphQL Queries Integration Tests › GraphQL Schema Introspection" name="should have Restaurant type in schema" time="0.054">
    </testcase>
  </testsuite>
  <testsuite name="Order GraphQL API Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-06-23T14:34:32" time="9.327" tests="6">
    <testcase classname="INTEGRATION.Order GraphQL API Integration Tests › Order Schema Types" name="should have Order type in schema" time="0.224">
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL API Integration Tests › Order Schema Types" name="should have OrderStatus enum in schema" time="0.046">
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL API Integration Tests › Order Input Types" name="should have OrderInput type in schema" time="0.034">
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL API Integration Tests › Order Input Types" name="should validate GraphQL order operations" time="0.052">
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL API Integration Tests › GraphQL Error Handling" name="should handle malformed order queries" time="0.055">
    </testcase>
    <testcase classname="INTEGRATION.Order GraphQL API Integration Tests › GraphQL Error Handling" name="should validate required arguments" time="0.042">
    </testcase>
  </testsuite>
</testsuites>